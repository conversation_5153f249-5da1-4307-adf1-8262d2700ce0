<script setup lang="ts">
  import { computed, onUnmounted, ref, watch } from 'vue';

  import { message } from 'ant-design-vue';

  import { updateVoucherDetailById } from '#/api/jsj-ai/api-v2';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  import AddAuxiliaryPop from '../../bookkeeping/enter/AddAuxiliaryPop.vue';
  import AddSubjectPop from '../../bookkeeping/enter/AddSubjectPop.vue';
  import emitter from '../../bookkeeping/enter/usermitt';
  import AuxiliaryProjectSelector from './AuxiliaryProjectSelector.vue';
  import CounterpartAccountSelector from './CounterpartAccountSelector.vue';

  interface Props {
    bankReceiptData?: any;
    visible: boolean;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'success'): void;
    (e: 'cancel'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    bankReceiptData: null,
    visible: false,
  });

  const emit = defineEmits<Emits>();

  // 表单数据
  const formData = ref({
    auxiliaryProject: '',
    counterpartAccount: '',
  });

  const loading = ref(false);
  const addSubjectRef = ref<any>(null);
  const addAuxiliaryRef = ref<any>(null);

  // 使用会计科目 hooks
  const {
    accountSubjects,
    addLocalAuxiliary,
    addLocalSubject,
    getAssistantOptions,
  } = useAccountSubjects();

  // 获取当前选中的对方科目信息
  const selectedAccountInfo = computed(() => {
    if (!formData.value.counterpartAccount || !accountSubjects.value)
      return null;

    return accountSubjects.value.find(
      (subject) => subject.id === Number(formData.value.counterpartAccount),
    );
  });

  // 判断对方科目是否开启辅助核算
  const isCounterpartAuxiliaryEnabled = computed(() => {
    if (selectedAccountInfo.value) {
      return (
        selectedAccountInfo.value.useAssistant === true ||
        !!selectedAccountInfo.value.assistantType
      );
    }
    // 如果还没选择科目，或者科目信息正在加载，则让选择器自己处理状态
    return true;
  });

  // 获取当前银行回单的对方科目信息
  const currentOtherSideDetail = computed(() => {
    if (!props.bankReceiptData?.voucher_detail) return null;

    return props.bankReceiptData.voucher_detail.find(
      (detail: any) => detail.other_side === true,
    );
  });

  // 监听弹窗显示状态，初始化表单数据
  watch(
    () => props.visible,
    (newVisible) => {
      if (newVisible && props.bankReceiptData) {
        initFormData();
      }
    },
  );

  // 初始化表单数据
  const initFormData = () => {
    const otherSideDetail = currentOtherSideDetail.value;

    if (otherSideDetail) {
      // 设置对方会计科目
      formData.value.counterpartAccount =
        otherSideDetail.account_id?.toString() || '';

      // 设置辅助项目
      formData.value.auxiliaryProject =
        otherSideDetail.auxiliary && otherSideDetail.auxiliary.length > 0
          ? otherSideDetail.auxiliary[0].id?.toString() || ''
          : '';
    } else {
      // 重置表单
      formData.value.counterpartAccount = '';
      formData.value.auxiliaryProject = '';
    }
  };

  // 处理对方会计科目变化
  const handleAccountChange = (value: string) => {
    formData.value.counterpartAccount = value;
    // 清空辅助项目选择
    formData.value.auxiliaryProject = '';
  };

  // 处理辅助项目变化
  const handleAuxiliaryChange = (value: string) => {
    formData.value.auxiliaryProject = value;
  };

  // 新增科目
  const handleAddSubject = () => {
    // 获取当前选中的对方会计科目作为上级科目
    const currentSubject = selectedAccountInfo.value;
    addSubjectRef.value?.open(currentSubject);
  };

  // 新增辅助项目
  const handleAddAuxiliary = () => {
    const currentSubject = selectedAccountInfo.value;
    const auxiliaryType = currentSubject?.assistantType || 'customer';

    // 尝试从银行回单数据中提取LLM可能输出的辅助项目值
    let llmAuxiliaryValue = '';

    // 可以从对方户名称中提取作为默认的辅助项目值
    if (props.bankReceiptData?.counterparty_account_name) {
      llmAuxiliaryValue = props.bankReceiptData.counterparty_account_name;
    }

    addAuxiliaryRef.value?.open(
      currentSubject,
      auxiliaryType,
      llmAuxiliaryValue,
    );
  };

  // 监听新增科目事件
  const listenSubjectMitt = (data: any) => {
    console.log('收到新增科目事件:', data);
    addLocalSubject(data);
    // 自动选择新增的科目
    formData.value.counterpartAccount = data.value || data.id;
  };

  // 监听新增辅助项目事件
  const listenAuxiliaryMitt = (data: any) => {
    console.log('收到新增辅助项目事件:', data);

    if (data.auxiliary) {
      addLocalAuxiliary(data.auxiliary);
      // 自动选择新增的辅助项目
      formData.value.auxiliaryProject = data.auxiliary.id?.toString() || '';
    } else {
      addLocalAuxiliary(data);
      formData.value.auxiliaryProject = data.id?.toString() || '';
    }
  };

  // 保存修改
  const handleSave = async () => {
    if (!props.bankReceiptData?.voucher_id) {
      message.error('缺少凭证ID，无法保存');
      return;
    }

    loading.value = true;
    try {
      // 构建更新的凭证详情数据
      const updatedDetails = [...(props.bankReceiptData.voucher_detail || [])];

      // 找到对方科目的详情并更新
      const otherSideIndex = updatedDetails.findIndex(
        (detail: any) => detail.other_side === true,
      );

      if (otherSideIndex !== -1 && formData.value.counterpartAccount) {
        const selectedAccount = selectedAccountInfo.value;
        if (selectedAccount) {
          // 获取辅助项目信息
          let auxiliaryData: any[] = [];
          if (formData.value.auxiliaryProject) {
            // 从辅助项目选项中获取完整信息
            const assistantOptions = getAssistantOptions(
              selectedAccount.assistantType || 'customer',
            );
            const selectedAuxiliary = assistantOptions.find(
              (aux) => aux.id === Number(formData.value.auxiliaryProject),
            );

            if (selectedAuxiliary) {
              auxiliaryData = [
                {
                  code: selectedAuxiliary.code,
                  id: selectedAuxiliary.id,
                  name: selectedAuxiliary.name,
                  type: selectedAccount.assistantType || 'customer',
                },
              ];
            }
          }

          // 更新对方科目信息
          updatedDetails[otherSideIndex] = {
            ...updatedDetails[otherSideIndex],
            account: selectedAccount.name,
            account_code: selectedAccount.code,
            account_id: selectedAccount.id,
            auxiliary: auxiliaryData,
          };
        }
      }

      // 调用更新接口
      await updateVoucherDetailById({
        details: updatedDetails,
        voucher_id: props.bankReceiptData.voucher_id,
      });

      message.success('保存成功');
      emit('success');
      handleCancel();
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error?.message || '保存失败');
    } finally {
      loading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    emit('update:visible', false);
    emit('cancel');
  };

  // 组件挂载时监听事件
  emitter.on('account_voucher_subject_added', listenSubjectMitt);
  emitter.on('account_voucher_auxiliary_added', listenAuxiliaryMitt);

  // 组件卸载时移除监听
  onUnmounted(() => {
    emitter.off('account_voucher_subject_added', listenSubjectMitt);
    emitter.off('account_voucher_auxiliary_added', listenAuxiliaryMitt);
  });
</script>

<template>
  <a-modal
    :visible="visible"
    title="编辑对方会计科目和辅助项目"
    width="600px"
    :confirm-loading="loading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <div class="bank-receipt-edit-form">
      <!-- 银行回单基本信息 -->
      <div v-if="bankReceiptData" class="receipt-info">
        <h4>银行回单信息</h4>
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="回单编号">
            {{ bankReceiptData.transaction_id }}
          </a-descriptions-item>
          <a-descriptions-item label="交易时间">
            {{ bankReceiptData.transaction_time }}
          </a-descriptions-item>
          <a-descriptions-item label="摘要">
            {{ bankReceiptData.summary }}
          </a-descriptions-item>
          <a-descriptions-item label="金额">
            {{ bankReceiptData.amount }} {{ bankReceiptData.currency }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 编辑表单 -->
      <div class="edit-form">
        <h4>编辑对方科目信息</h4>
        <a-form layout="vertical">
          <a-form-item label="对方会计科目" required>
            <CounterpartAccountSelector
              v-model="formData.counterpartAccount"
              @change="handleAccountChange"
              @add-subject="handleAddSubject"
            />
          </a-form-item>
          <a-form-item label="辅助项目">
            <a-input
              v-if="
                formData.counterpartAccount && !isCounterpartAuxiliaryEnabled
              "
              disabled
              value="未开启辅助核算"
            />
            <AuxiliaryProjectSelector
              v-else
              v-model="formData.auxiliaryProject"
              :selected-account-id="formData.counterpartAccount"
              @change="handleAuxiliaryChange"
              @add-auxiliary="handleAddAuxiliary"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>

    <!-- 新增科目弹框 -->
    <AddSubjectPop ref="addSubjectRef" />

    <!-- 新增辅助项目弹框 -->
    <AddAuxiliaryPop ref="addAuxiliaryRef" />
  </a-modal>
</template>

<style lang="scss" scoped>
  .bank-receipt-edit-form {
    .receipt-info {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 12px;
        font-weight: 600;
      }
    }

    .edit-form {
      h4 {
        margin-bottom: 16px;
        font-weight: 600;
      }
    }
  }
</style>
